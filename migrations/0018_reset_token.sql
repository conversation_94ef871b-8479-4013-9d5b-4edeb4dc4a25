-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS reset_tokens (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses and client interactions (generated by backend)
    user_id INTEGER UNIQUE REFERENCES users(id) ON DELETE CASCADE, -- Reference to user internal ID
    token TEXT NOT NULL, -- Reset token
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL, -- Token expiration timestamp
    used_at TIMESTAMP WITH TIME ZONE -- Token used timestamp (NULL if not used)
);
CREATE INDEX IF NOT EXISTS idx_reset_tokens_external_id ON reset_tokens(external_id);
CREATE INDEX IF NOT EXISTS idx_reset_tokens_user_id ON reset_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_reset_tokens_token ON reset_tokens(token);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP INDEX IF EXISTS idx_reset_tokens_token;
DROP INDEX IF EXISTS idx_reset_tokens_user_id;
DROP INDEX IF EXISTS idx_reset_tokens_external_id;
DROP TABLE IF EXISTS reset_tokens;
-- +goose StatementEnd
