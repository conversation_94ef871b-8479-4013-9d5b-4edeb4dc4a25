package errorbank

import (
	"errors"
	"net/http"
	"testing"
)

func TestNotFound(t *testing.T) {
	err := NotFound("user")

	if err.Code() != CodeNotFound {
		t.Fatalf("expected code %s, got %s", CodeNotFound, err.Code())
	}
	if err.Resource() != "user" {
		t.Fatalf("expected resource user, got %s", err.Resource())
	}
	if err.Status() != http.StatusNotFound {
		t.Fatalf("expected status %d, got %d", http.StatusNotFound, err.Status())
	}
	if got := err.Error(); got != "user not found" {
		t.Fatalf("unexpected message: %s", got)
	}
}

func TestOptions(t *testing.T) {
	cause := errors.New("boom")

	err := AlreadyExists("currency", WithMessage("currency code exists"), WithCause(cause), WithDetail("code", "NGN"))

	if err.Message() != "currency code exists" {
		t.Fatalf("unexpected message: %s", err.Message())
	}
	if !errors.Is(err, cause) {
		t.Fatal("expected errors.Is to unwrap cause")
	}
	if err.Status() != http.StatusConflict {
		t.Fatalf("expected status %d, got %d", http.StatusConflict, err.Status())
	}
	details := err.Details()
	if details == nil {
		t.Fatal("expected details map to be initialised")
	}
	if details["code"] != "NGN" {
		t.Fatalf("expected detail to be preserved, got %+v", details)
	}
	details["code"] = "mutated"
	if fresh := err.Details()["code"]; fresh != "NGN" {
		t.Fatalf("expected details to be defensive copy, got %s", fresh)
	}
}

func TestStatusFrom(t *testing.T) {
	err := InvalidArgument("payload")

	status, ok := StatusFrom(err)
	if !ok {
		t.Fatal("expected status to be reported")
	}
	if status != http.StatusBadRequest {
		t.Fatalf("expected status %d, got %d", http.StatusBadRequest, status)
	}
}

func TestNew(t *testing.T) {
	err := New(CodeInvalidArgument, "bad input")

	if err.Status() != http.StatusBadRequest {
		t.Fatalf("expected default status %d, got %d", http.StatusBadRequest, err.Status())
	}
	if err.Error() != "bad input" {
		t.Fatalf("unexpected error string: %s", err.Error())
	}
}

func TestExtractAndIs(t *testing.T) {
	err := Forbidden()

	if !Is(err, CodeForbidden) {
		t.Fatal("expected Is to detect forbidden code")
	}
	derived, ok := Extract(err)
	if !ok {
		t.Fatal("expected Extract to succeed")
	}
	if derived != err {
		t.Fatal("expected Extract to return original pointer")
	}
}
