# errorbank

`errorbank` offers a simple way to standardise error handling across the
project. It provides enriched errors that carry a machine-readable code,
HTTP status hint, contextual details, and optional wrapped causes so that
handlers and middleware can make consistent decisions.

## Features

- Canonical constructors such as `errorbank.NotFound("user")` and
  `errorbank.InvalidArgument("payload")` ensure uniform messages and
  status codes.
- Rich `errorbank.Error` type exposes `Code()`, `Message()`, `Status()`,
  `Resource()`, and `Details()` helpers.
- Functional options (`WithMessage`, `WithCause`, `WithDetail`, …) let
  you add context without losing the defaults.
- Helpers like `errorbank.Is(err, errorbank.CodeNotFound)` or
  `errorbank.StatusFrom(err)` simplify branching logic.

## Quick Start

```go
import "telescope-be/pkg/errorbank"

func getUser(id string) (*User, error) {
    user, err := repo.FindUser(id)
    if errors.Is(err, sql.ErrNoRows) {
        return nil, errorbank.NotFound("user",
            errorbank.WithDetail("id", id),
        )
    }
    if err != nil {
        return nil, errorbank.Internal("failed to fetch user",
            errorbank.WithCause(err),
        )
    }
    return user, nil
}
```

## HTTP Integration Example

The `internal/appctx.NewErrorResponseBuilder` turns any `errorbank` error into a
standard response payload. Typical usage inside a handler looks like this:

```go
func (h *Handler) Handle(c echo.Context) error {
    if err := h.service.DoSomething(); err != nil {
        return appctx.NewErrorResponseBuilder(err).Build(c)
    }
    return appctx.NewResponseBuilder().
        WithCode(http.StatusOK).
        WithMessage("ok").
        Build(c)
}
```

## Adding New Error Shapes

1. Define a new constant in `errorbank.Code` if needed.
2. Add a constructor (see `errorbank.NotFound`) that chooses a default
   message and status.
3. Consider whether handlers need special-case helpers (e.g. the
   response builder already understands `ErrorCode`, `Status`, and
   `Details`).

## Testing

Unit tests live in `pkg/errorbank/error_test.go`. Run them with:

```bash
go test ./pkg/errorbank
```

If the local Go toolchain version does not match the project’s go.mod
version, align the toolchain or export `GOTOOLCHAIN` accordingly before
running tests.

## Migration Tips

- Replace manual `errors.New("user not found")` occurrences with
  `errorbank.NotFound("user")` (add option-backed details when useful).
- Update HTTP handlers to build responses using the `ErrorResponseBuilder`
  so status codes and payload shape remain consistent.
- When wrapping third-party errors, prefer `errorbank.WithCause(err)` so
  upstream diagnostics remain available through `errors.Is` / `errors.As`.
