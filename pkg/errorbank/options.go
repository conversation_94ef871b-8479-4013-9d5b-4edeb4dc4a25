package errorbank

import "strings"

// Option customises the error created by the helpers in this package.
type Option func(*Error)

// WithMessage overrides the default message.
func WithMessage(message string) Option {
	return func(e *Error) {
		if m := strings.TrimSpace(message); m != "" {
			e.message = m
		}
	}
}

// WithCause attaches an underlying error that will be available through errors.Unwrap.
func WithCause(err error) Option {
	return func(e *Error) {
		if err != nil {
			e.cause = err
		}
	}
}

// WithStatus overrides the HTTP status code associated with the error.
func WithStatus(status int) Option {
	return func(e *Error) {
		e.status = status
	}
}

// WithResource overrides the resource label attached to the error.
func WithResource(resource string) Option {
	return func(e *Error) {
		if r := sanitizeResource(resource); r != "" {
			e.resource = r
		}
	}
}

// WithDetail adds a key/value pair that callers may use to provide additional context.
func WithDetail(key string, value any) Option {
	return func(e *Error) {
		if k := strings.TrimSpace(key); k != "" {
			if e.details == nil {
				e.details = make(map[string]any)
			}
			e.details[k] = value
		}
	}
}

// WithDetails merges all the pairs from the provided map into the error details.
func WithDetails(details map[string]any) Option {
	return func(e *Error) {
		if len(details) == 0 {
			return
		}
		if e.details == nil {
			e.details = make(map[string]any, len(details))
		}
		for key, value := range details {
			if k := strings.TrimSpace(key); k != "" {
				e.details[k] = value
			}
		}
	}
}
