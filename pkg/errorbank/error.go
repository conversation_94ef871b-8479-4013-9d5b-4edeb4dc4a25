// Package errorbank provides helpers to create consistent rich errors that
// carry codes, HTTP status hints and contextual details.
package errorbank

import (
	"errors"
	"fmt"
	"net/http"
	"strings"
)

// New builds an Error with the provided code and message.
func New(code Code, message string, opts ...Option) *Error {
	return buildError(code, "", defaultStatusForCode(code), message, opts...)
}

// NotFound creates a standardised not found error for the given resource name.
func NotFound(resource string, opts ...Option) *Error {
	name := sanitizeResource(resource)
	message := fmt.Sprintf("%s not found", displayName(name, "resource"))
	return buildError(CodeNotFound, name, http.StatusNotFound, message, opts...)
}

// AlreadyExists creates a standardised conflict error when a resource is duplicated.
func AlreadyExists(resource string, opts ...Option) *Error {
	name := sanitizeResource(resource)
	message := fmt.Sprintf("%s already exists", displayName(name, "resource"))
	return buildError(CodeAlreadyExists, name, http.StatusConflict, message, opts...)
}

// InvalidArgument creates an error representing an invalid field or payload.
func InvalidArgument(argument string, opts ...Option) *Error {
	name := sanitizeResource(argument)
	message := fmt.Sprintf("%s is invalid", displayName(name, "argument"))
	return buildError(CodeInvalidArgument, name, http.StatusBadRequest, message, opts...)
}

// Unauthorized creates an error signalling that authentication is required.
func Unauthorized(opts ...Option) *Error {
	return buildError(CodeUnauthorized, "", http.StatusUnauthorized, "unauthorized", opts...)
}

// Forbidden creates an error signalling that an action is not permitted.
func Forbidden(opts ...Option) *Error {
	return buildError(CodeForbidden, "", http.StatusForbidden, "forbidden", opts...)
}

// Conflict creates an error representing a general conflict situation.
func Conflict(resource string, opts ...Option) *Error {
	name := sanitizeResource(resource)
	message := fmt.Sprintf("%s conflict", displayName(name, "resource"))
	return buildError(CodeConflict, name, http.StatusConflict, message, opts...)
}

// RateLimited indicates that the caller exceeded some rate limit.
func RateLimited(resource string, opts ...Option) *Error {
	name := sanitizeResource(resource)
	message := fmt.Sprintf("%s rate limited", displayName(name, "request"))
	return buildError(CodeRateLimited, name, http.StatusTooManyRequests, message, opts...)
}

// Internal returns an internal server error with a friendly default message.
func Internal(message string, opts ...Option) *Error {
	msg := strings.TrimSpace(message)
	if msg == "" {
		msg = "internal server error"
	}
	return buildError(CodeInternal, "", http.StatusInternalServerError, msg, opts...)
}

// Extract attempts to convert an error into *Error.
func Extract(err error) (*Error, bool) {
	if err == nil {
		return nil, false
	}
	var appErr *Error
	if errors.As(err, &appErr) {
		return appErr, true
	}
	return nil, false
}

// Is reports whether the provided error has the given Code. It unwraps errors.
func Is(err error, code Code) bool {
	if err == nil {
		return false
	}
	var appErr *Error
	if errors.As(err, &appErr) {
		return appErr.code == code
	}
	return false
}

// StatusFrom returns the HTTP status associated with the error, if available.
func StatusFrom(err error) (int, bool) {
	appErr, ok := Extract(err)
	if !ok {
		return 0, false
	}
	if appErr.status == 0 {
		return 0, false
	}
	return appErr.status, true
}
