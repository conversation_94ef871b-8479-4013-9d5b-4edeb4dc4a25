package errorbank

import (
	"net/http"
	"strings"
)

// Code represents a machine-readable error identifier that can be used
// to drive conditional logic or HTTP status mapping.
type Code string

const (
	CodeUnknown         Code = "unknown"
	CodeInvalidArgument Code = "invalid_argument"
	CodeNotFound        Code = "not_found"
	CodeAlreadyExists   Code = "already_exists"
	CodeUnauthorized    Code = "unauthorized"
	CodeForbidden       Code = "forbidden"
	CodeConflict        Code = "conflict"
	CodeInternal        Code = "internal"
	CodeRateLimited     Code = "rate_limited"
)

// Error is a rich error implementation that keeps the human-readable message,
// its code, optional resource identifier, HTTP status, extra details and the
// original cause if there is one.
type Error struct {
	code     Code
	message  string
	resource string
	status   int
	details  map[string]any
	cause    error
}

// Error satisfies the standard error interface.
func (e *Error) Error() string {
	return e.message
}

// Unwrap exposes the underlying cause when the error was created with WithCause.
func (e *Error) Unwrap() error {
	return e.cause
}

// Code returns the machine-readable code describing this error.
func (e *Error) Code() Code {
	return e.code
}

// Message returns the final message associated with the error.
func (e *Error) Message() string {
	return e.message
}

// Resource returns the resource related to the error, if any.
func (e *Error) Resource() string {
	return e.resource
}

// Status returns the HTTP status code associated with the error. The value can
// be zero when the error is not tied to a specific status code.
func (e *Error) Status() int {
	return e.status
}

// Details returns a defensive copy of the details map, if any was configured.
func (e *Error) Details() map[string]any {
	if len(e.details) == 0 {
		return nil
	}

	clone := make(map[string]any, len(e.details))
	for k, v := range e.details {
		clone[k] = v
	}
	return clone
}

// Cause returns the underlying error set through WithCause, if any.
func (e *Error) Cause() error {
	return e.cause
}

func buildError(code Code, resource string, status int, fallbackMessage string, opts ...Option) *Error {
	err := &Error{
		code:     code,
		resource: resource,
		status:   status,
		message:  strings.TrimSpace(fallbackMessage),
	}
	for _, opt := range opts {
		opt(err)
	}

	err.message = ensureMessage(err.message, fallbackMessage, code)
	err.resource = sanitizeResource(err.resource)

	return err
}

func sanitizeResource(resource string) string {
	resource = strings.TrimSpace(resource)
	return resource
}

func displayName(resource string, fallback string) string {
	if resource != "" {
		return resource
	}
	return fallback
}

func ensureMessage(current string, fallback string, code Code) string {
	if m := strings.TrimSpace(current); m != "" {
		return m
	}
	if f := strings.TrimSpace(fallback); f != "" {
		return f
	}
	if code != "" {
		return string(code)
	}
	return "error"
}

func defaultStatusForCode(code Code) int {
	switch code {
	case CodeInvalidArgument:
		return http.StatusBadRequest
	case CodeNotFound:
		return http.StatusNotFound
	case CodeAlreadyExists, CodeConflict:
		return http.StatusConflict
	case CodeUnauthorized:
		return http.StatusUnauthorized
	case CodeForbidden:
		return http.StatusForbidden
	case CodeRateLimited:
		return http.StatusTooManyRequests
	case CodeInternal, CodeUnknown:
		return http.StatusInternalServerError
	default:
		return 0
	}
}
