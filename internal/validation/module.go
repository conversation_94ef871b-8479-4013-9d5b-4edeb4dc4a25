// Package validation provides fx module for dependency injection of validation services.
package validation

import (
	"context"

	"go.uber.org/fx"
)

// Module provides validation dependencies for the application
var Module = fx.Module(
	"validation",
	fx.Provide(NewValidator),
	fx.Provide(NewValidationHelper),
	fx.Invoke(func(lc fx.Lifecycle, helper *ValidationHelper) {
		lc.Append(fx.Hook{
			OnStart: func(context.Context) error {
				setGlobalValidationHelper(helper)
				return nil
			},
		})
	}),
)
