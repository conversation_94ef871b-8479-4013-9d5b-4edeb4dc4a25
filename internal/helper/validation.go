// Package helper provides utility functions for validation and input sanitization.
package helper

import (
	"telescope-be/internal/validation"
)

const (
	// Username validation constraints
	MinUsernameLength = 3
	MaxUsernameLength = 255

	// Error messages
	errValidationHelperNotInitialized = "ValidationHelper not initialized - ensure fx module is properly configured"
)

// ValidationHelper provides validation utility functions with proper dependency injection
type ValidationHelper struct {
	validator validation.Validator
}

// NewValidationHelper creates a new validation helper with injected dependencies
func NewValidationHelper(validator validation.Validator) *ValidationHelper {
	return &ValidationHelper{
		validator: validator,
	}
}

// IsValidUsername validates if a string is a valid username
// Rules:
// - Length between 3-255 characters
// - Must start and end with alphanumeric characters
// - Can contain alphanumeric, underscore, hyphen, and dot
// - Cannot have consecutive special characters
func (h *ValidationHelper) IsValidUsername(username string) bool {
	return h.validator.IsValidUsername(username)
}

// IsValidEmail validates if a string is a valid email address
// This provides basic validation, but should be used alongside
// go-playground/validator for comprehensive validation
func (h *ValidationHelper) IsValidEmail(email string) bool {
	return h.validator.IsValidEmail(email)
}

// IsEmailOrUsername determines if an identifier is an email or username
// Returns "email", "username", or "invalid"
func (h *ValidationHelper) IsEmailOrUsername(identifier string) string {
	return h.validator.IsEmailOrUsername(identifier)
}

// SanitizeIdentifier cleans and normalizes an identifier
// Trims whitespace and converts to lowercase for consistent lookup
func (h *ValidationHelper) SanitizeIdentifier(identifier string) string {
	return h.validator.SanitizeIdentifier(identifier)
}

// ValidateLoginIdentifier validates that an identifier is either a valid email or username
// Returns the type ("email" or "username") and any validation error
func (h *ValidationHelper) ValidateLoginIdentifier(identifier string) (string, error) {
	if identifier == "" {
		return "", NewValidationError("identifier is required")
	}

	// Use the validator service for detailed error messages
	if validationErr := h.validator.FieldWithDetails(identifier, "email_or_username", "identifier"); validationErr != nil {
		return "", NewValidationError(validationErr.Message)
	}

	identifierType := h.validator.IsEmailOrUsername(identifier)
	return identifierType, nil
}

// Backward compatibility functions that use a global instance
// These will be deprecated once all usage is migrated to the injected service

var globalValidationHelper *ValidationHelper

// setGlobalValidationHelper sets the global validation helper instance
// This is used internally by the fx module for backward compatibility
func setGlobalValidationHelper(helper *ValidationHelper) {
	globalValidationHelper = helper
}

// IsValidUsername validates if a string is a valid username (backward compatibility)
func IsValidUsername(username string) bool {
	if globalValidationHelper == nil {
		panic(errValidationHelperNotInitialized)
	}
	return globalValidationHelper.IsValidUsername(username)
}

// IsValidEmail validates if a string is a valid email address (backward compatibility)
func IsValidEmail(email string) bool {
	if globalValidationHelper == nil {
		panic(errValidationHelperNotInitialized)
	}
	return globalValidationHelper.IsValidEmail(email)
}

// IsEmailOrUsername determines if an identifier is an email or username (backward compatibility)
func IsEmailOrUsername(identifier string) string {
	if globalValidationHelper == nil {
		panic(errValidationHelperNotInitialized)
	}
	return globalValidationHelper.IsEmailOrUsername(identifier)
}

// SanitizeIdentifier cleans and normalizes an identifier (backward compatibility)
func SanitizeIdentifier(identifier string) string {
	if globalValidationHelper == nil {
		panic(errValidationHelperNotInitialized)
	}
	return globalValidationHelper.SanitizeIdentifier(identifier)
}

// ValidateLoginIdentifier validates that an identifier is either a valid email or username (backward compatibility)
func ValidateLoginIdentifier(identifier string) (string, error) {
	if globalValidationHelper == nil {
		panic(errValidationHelperNotInitialized)
	}
	return globalValidationHelper.ValidateLoginIdentifier(identifier)
}

// ValidationError represents a validation error
type ValidationError struct {
	Message string
}

func (e *ValidationError) Error() string {
	return e.Message
}

// NewValidationError creates a new validation error
func NewValidationError(message string) *ValidationError {
	return &ValidationError{Message: message}
}

// IsValidationError checks if an error is a validation error
func IsValidationError(err error) bool {
	_, ok := err.(*ValidationError)
	return ok
}
