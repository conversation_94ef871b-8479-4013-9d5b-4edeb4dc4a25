// Package helper provides fx module for dependency injection of helper services.
package helper

import (
	"context"

	"go.uber.org/fx"
)

// Module provides helper dependencies for the application
var Module = fx.Module(
	"helper",
	fx.Provide(NewValidationHelper),
	fx.Invoke(func(lc fx.Lifecycle, helper *ValidationHelper) {
		lc.Append(fx.Hook{
			OnStart: func(context.Context) error {
				setGlobalValidationHelper(helper)
				return nil
			},
		})
	}),
)
