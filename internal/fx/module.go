// Package fx provides dependency injection modules using Uber FX.
package fx

import (
	"strings"

	"telescope-be/pkg/mailer"

	echo "github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"go.uber.org/fx"
	"go.uber.org/zap"

	"telescope-be/internal/appctx"
	"telescope-be/internal/database"
	"telescope-be/internal/handler/http"
	"telescope-be/internal/handler/http/apidocs"
	authHandler "telescope-be/internal/handler/http/auth"
	duckHandler "telescope-be/internal/handler/http/duck"
	healthHandler "telescope-be/internal/handler/http/health"
	resetHandler "telescope-be/internal/handler/http/resettoken"
	userHandler "telescope-be/internal/handler/http/user"
	"telescope-be/internal/helper"
	"telescope-be/internal/repository"
	"telescope-be/internal/routes"
	"telescope-be/internal/server"
	"telescope-be/internal/service"
	authService "telescope-be/internal/service/auth"
	duckService "telescope-be/internal/service/duck"
	resetService "telescope-be/internal/service/resettoken"
	userService "telescope-be/internal/service/user"
	"telescope-be/internal/validation"
)

// NewLogger creates a new zap logger instance
func NewLogger(config *appctx.Config) (*zap.Logger, error) {
	var logger *zap.Logger
	var err error

	switch config.Logger.Level {
	case "DEBUG":
		logger, err = zap.NewDevelopment()
	default:
		logger, err = zap.NewProduction()
	}

	return logger, err
}

// NewEcho creates a new Echo instance with middleware
func NewEcho(_ *appctx.Config, _ *zap.Logger, validator validation.Validator) *echo.Echo {
	e := echo.New()

	// Add default middleware
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())

	// Register custom validator
	e.Validator = appctx.NewCustomValidator(validator)

	return e
}

// NewMailer constructs a mailer instance from application configuration
func NewMailer(config *appctx.Config) (mailer.Mailer, error) {
	mailerConfig := mailer.Config{
		Provider:      mailer.Provider(strings.ToLower(strings.TrimSpace(config.Mailer.Provider))),
		APIKey:        strings.TrimSpace(config.Mailer.APIKey),
		FromEmail:     strings.TrimSpace(config.Mailer.FromEmail),
		FromName:      strings.TrimSpace(config.Mailer.FromName),
		TemplatesPath: strings.TrimSpace(config.Mailer.TemplatesPath),
		Debug:         config.Mailer.Debug,
	}

	if mailerConfig.FromName == "" {
		mailerConfig.FromName = "No Reply"
	}

	return mailer.New(mailerConfig)
}

// Module provides all the dependencies for the application
var Module = fx.Options(
	// Configuration
	fx.Provide(appctx.LoadConfig),

	// Logger
	fx.Provide(NewLogger),

	// Echo instance
	fx.Provide(NewEcho),

	// Database
	fx.Provide(database.NewDatabaseConnection),

	// Validation
	validation.Module,

	// Helper services
	helper.Module,

	// Packages
	fx.Provide(NewMailer),

	// Repository layer
	fx.Provide(repository.NewRepository),

	// Helper layer
	fx.Provide(helper.NewJWTHelperFromConfig),

	// Service layer
	fx.Provide(authService.NewAuthService),
	fx.Provide(duckService.NewDuckService),
	fx.Provide(resetService.NewService),
	fx.Provide(userService.NewUserService),
	fx.Provide(service.NewService),

	// Handler layer
	fx.Provide(authHandler.NewAuthHandler),
	fx.Provide(duckHandler.NewDuckHandler),
	fx.Provide(healthHandler.NewHealthHandler),
	fx.Provide(resetHandler.NewResetTokenHandler),
	fx.Provide(userHandler.NewUserHandler),
	fx.Provide(apidocs.NewAPIDocsHandler),
	fx.Provide(http.NewHTTPHandler),

	// Routes
	fx.Provide(routes.NewRoutes),

	// Server
	fx.Provide(server.NewHTTPServer),
)
