package appctx

import (
	"telescope-be/internal/validation"
)

// CustomValidator is a custom validator for Echo
type CustomValidator struct {
	Validator validation.Validator
}

// Validate validates the provided struct
func (cv *CustomValidator) Validate(i any) error {
	return cv.Validator.Struct(i)
}

// NewCustomValidator creates a new custom validator with injected dependencies
func NewCustomValidator(validator validation.Validator) *CustomValidator {
	return &CustomValidator{
		Validator: validator,
	}
}
