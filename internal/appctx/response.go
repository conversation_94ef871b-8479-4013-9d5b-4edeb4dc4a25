// Package appctx provides application context utilities and configuration management.
package appctx

import (
	"net/http"
	"strings"

	echo "github.com/labstack/echo/v4"

	"telescope-be/pkg/errorbank"
)

type Response struct {
	Code      int            `json:"code"`
	Message   string         `json:"message"`
	Data      any            `json:"data"`
	Error     string         `json:"error,omitempty"`
	Errors    []string       `json:"errors,omitempty"`
	Meta      any            `json:"meta,omitempty"`
	ErrorCode string         `json:"error_code,omitempty"`
	Details   map[string]any `json:"details,omitempty"`
}

// ResponseBuilder provides a fluent interface for building Response objects
type ResponseBuilder struct {
	response *Response
}

// NewResponseBuilder creates a new ResponseBuilder instance
func NewResponseBuilder() *ResponseBuilder {
	return &ResponseBuilder{
		response: &Response{},
	}
}

// WithCode sets the response code
func (rb *ResponseBuilder) WithCode(code int) *ResponseBuilder {
	rb.response.Code = code
	return rb
}

// WithMessage sets the response message
func (rb *ResponseBuilder) WithMessage(message string) *ResponseBuilder {
	rb.response.Message = message
	return rb
}

// WithData sets the response data
func (rb *ResponseBuilder) WithData(data any) *ResponseBuilder {
	rb.response.Data = data
	return rb
}

// WithError sets a single error message
func (rb *ResponseBuilder) WithError(errMsg string) *ResponseBuilder {
	rb.response.Error = errMsg
	return rb
}

// WithErrors sets multiple error messages
func (rb *ResponseBuilder) WithErrors(errors []string) *ResponseBuilder {
	rb.response.Errors = errors
	return rb
}

// WithMeta sets the response metadata
func (rb *ResponseBuilder) WithMeta(meta any) *ResponseBuilder {
	rb.response.Meta = meta
	return rb
}

// WithErrorCode assigns a machine-readable error code to the response.
func (rb *ResponseBuilder) WithErrorCode(code string) *ResponseBuilder {
	code = strings.TrimSpace(code)
	if code == "" {
		return rb
	}
	rb.response.ErrorCode = code
	return rb
}

// WithDetail adds a contextual key/value pair to the response details map.
func (rb *ResponseBuilder) WithDetail(key string, value any) *ResponseBuilder {
	key = strings.TrimSpace(key)
	if key == "" {
		return rb
	}
	if rb.response.Details == nil {
		rb.response.Details = make(map[string]any)
	}
	rb.response.Details[key] = value
	return rb
}

// WithDetails merges multiple contextual key/value pairs into the response details.
func (rb *ResponseBuilder) WithDetails(details map[string]any) *ResponseBuilder {
	if len(details) == 0 {
		return rb
	}
	for key, value := range details {
		rb.WithDetail(key, value)
	}
	return rb
}

// Build returns the constructed Response
func (rb *ResponseBuilder) Build(c echo.Context) error {
	return c.JSON(rb.response.Code, rb.response)
}

// BuildResponse returns the constructed Response as a value (not pointer)
func (rb *ResponseBuilder) BuildResponse() Response {
	return *rb.response
}

// ErrorResponseBuilder specialises ResponseBuilder for standardized error payloads.
type ErrorResponseBuilder struct {
	*ResponseBuilder
	source error
}

// NewErrorResponseBuilder creates a ResponseBuilder pre-populated from an errorbank error.
func NewErrorResponseBuilder(err error) *ErrorResponseBuilder {
	erb := &ErrorResponseBuilder{
		ResponseBuilder: NewResponseBuilder(),
		source:          err,
	}
	erb.populate()
	return erb
}

func (erb *ErrorResponseBuilder) populate() {
	baseErr := erb.source
	if baseErr == nil {
		baseErr = errorbank.Internal("")
	}

	status := http.StatusInternalServerError
	message := http.StatusText(status)
	code := errorbank.CodeUnknown

	if baseErr != nil {
		if msg := baseErr.Error(); strings.TrimSpace(msg) != "" {
			message = msg
		}
	}

	if appErr, ok := errorbank.Extract(baseErr); ok {
		if msg := strings.TrimSpace(appErr.Message()); msg != "" {
			message = msg
		}
		if s := appErr.Status(); s > 0 {
			status = s
		}
		if c := appErr.Code(); c != "" {
			code = c
		}
		details := appErr.Details()
		if res := strings.TrimSpace(appErr.Resource()); res != "" {
			if details == nil {
				details = make(map[string]any)
			}
			if _, exists := details["resource"]; !exists {
				details["resource"] = res
			}
		}
		if len(details) > 0 {
			erb.WithDetails(details)
		}
	}

	erb.WithCode(status)
	erb.WithMessage(message)
	erb.WithError(message)
	erb.WithErrorCode(string(code))
}

// Error returns the underlying error used to construct the builder.
func (erb *ErrorResponseBuilder) Error() error {
	return erb.source
}
